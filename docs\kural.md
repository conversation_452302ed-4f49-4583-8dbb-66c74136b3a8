ÖNCE BU KURALLARI ÖZÜMSE HAFIZANI TAZELE KOMPLE 

## KLASÖR YAPISI

Backend:
/routes     → auth.routes.ts
/services   → auth.service.ts  
/controllers → auth.controller.ts
/middlewares → auth.middleware.ts

Frontend:
/pages      → Dashboard/index.tsx
/components → common/Button.tsx, features/Cart.tsx
/hooks      → useAuth.ts
/services   → api.service.ts
/store      → authStore.ts

## İSİMLENDİRME

- Component: PascalCase → ProductCard.tsx
- Service/Hook: camelCase → useAuth.ts, authService.ts
- Types: PascalCase → Product.types.ts

## KURALLAR

1. Business logic → services'de
2. UI logic → components'de  
3. API calls → services'de
4. State → Zustand store'da
5. Her dosya tek iş yapsın

## YASAK

- 500+ satır dosya ❌
- Any type kullanma ❌
- Components'de API call ❌
- Inline styles ❌
## SOLID PRENSİPLERİ

1. **S** - Single Responsibility: Her class/fonksiyon TEK İŞ yapsın
2. **O** - Open/Closed: Yeni özellik eklerken eski kodu değiştirme
3. **L** - Liskov Substitution: Alt class'lar üst class'ın yerine geçebilmeli
4. **I** - Interface Segregation: Gereksiz method zorlama, interface'leri böl
5. **D** - Dependency Inversion: Concrete class'a değil, interface'e bağlan


🎯 API TEST KURALLARI - ÖZET
1. SERVER ÖNCE
npm run dev  # Background'da çalıştır
2. POWERSHELL SYNTAX
# JSON gönderirken MUTLAKA
-ContentType "application/json"

# Body hazırlama
$body = @{ key = "value" } | ConvertTo-Json
3. TEST SIRASI
Health check
Public endpoints
Auth (login)
Protected endpoints
4. ERROR HANDLING
try { 
    Invoke-RestMethod ... 
} catch { 
    $_.Exception.Response.StatusCode 
}
5. COMMON HATALAR
Server çalışmıyor ❌
Content-Type eksik ❌
Hardcode ID kullanma ❌
JSON syntax hatası ❌
6. GOLDEN RULE
Server çalıştır → Step by step → Content-Type unutma → Real data kullan

Frontendde Radix UI + Tailwind CSS v3 kullan.

## MODAL KURALLARI 🚨

### RENK SİSTEMİ
- Input'lar: `bg-kiosk-input` (koyu gri #374151) ❌ bg-white YASAK
- Card'lar: `bg-kiosk-card` (beyaz #ffffff)
- Text: `text-kiosk-text` (koyu #1f2937)
- Primary: `text-kiosk-primary` / `focus:ring-kiosk-primary`
- Border: `border-kiosk-border`

### MODAL YAPISI
```tsx
<Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-kiosk-card rounded-lg shadow-xl z-50 w-full max-w-2xl max-h-[90vh] flex flex-col">
  {/* SABİT HEADER */}
  <div className="flex items-center justify-between p-6 pb-4 border-b border-kiosk-border flex-shrink-0">
    <Dialog.Title>Başlık</Dialog.Title>
    <Dialog.Close><X /></Dialog.Close>
  </div>

  {/* SCROLLABLE İÇERİK */}
  <div className="flex-1 overflow-y-auto">
    <form className="p-6 space-y-6">
      {/* Form içeriği */}
    </form>
  </div>
</Dialog.Content>
```

### INPUT KURALLARI
```tsx
// ✅ DOĞRU
<input className="w-full px-3 py-2 border rounded-md text-sm bg-kiosk-input focus:ring-kiosk-primary" />

// ❌ YANLIŞ
<input className="w-full px-3 py-2 border rounded-md text-sm bg-white focus:ring-blue-500" />
```

### CHECKBOX KURALLARI
```tsx
// ✅ DOĞRU
<input type="checkbox" className="rounded border-kiosk-border text-kiosk-primary focus:ring-kiosk-primary" />

// ❌ YANLIŞ
<input type="checkbox" className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
```

## GOLDEN RULES 🏆
1. **Modal header MUTLAKA sabit olsun** (`flex-shrink-0`)
2. **Input'lar MUTLAKA koyu gri** (`bg-kiosk-input`)
3. **Focus ring MUTLAKA kiosk-primary** (`focus:ring-kiosk-primary`)
4. **Eski renkler YASAK** (blue-500, gray-300, white bg)
5. **Modal overflow sadece content'te** (header'da değil)