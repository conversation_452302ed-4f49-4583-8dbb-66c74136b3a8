import { PrismaClient } from '@prisma/client';
import { createError } from '../middlewares/error.middleware.js';
import type { 
  CreateModifierGroupInput, 
  UpdateModifierGroupInput,
  CreateModifierInput,
  UpdateModifierInput,
  GetModifierGroupsQuery,
  GetModifiersQuery
} from '../validators/modifier.validator.js';

export class ModifierService {
  constructor(private prisma: PrismaClient) {}

  // ==================== MODIFIER GROUP METHODS ====================

  async createModifierGroup(data: CreateModifierGroupInput) {
    // Business Rule: Modifiyer grup adı benzersiz olmalıdır
    const existingGroup = await this.prisma.modifierGroup.findFirst({
      where: {
        name: data.name,
        active: true
      }
    });

    if (existingGroup) {
      throw createError('Bu isimde bir modifiyer grubu zaten mevcut', 400);
    }

    const group = await this.prisma.modifierGroup.create({
      data,
      include: {
        _count: {
          select: {
            modifiers: {
              where: { active: true }
            }
          }
        }
      }
    });

    return {
      success: true,
      data: group
    };
  }

  async getModifierGroups(query: GetModifierGroupsQuery) {
    const { page, limit, search, active, includeModifiers } = query;
    const skip = (Number(page || 1) - 1) * Number(limit || 20);

    // Build where clause
    const where: any = {};

    if (active !== undefined) {
      where.active = active;
    } else {
      where.active = true; // Default olarak sadece aktif gruplar
    }

    if (search) {
      where.name = { contains: search, mode: 'insensitive' };
    }

    // Include options
    const include: any = {
      _count: {
        select: {
          modifiers: {
            where: { active: true }
          }
        }
      }
    };

    if (includeModifiers) {
      include.modifiers = {
        where: { active: true },
        select: {
          id: true,
          name: true,
          price: true,
          displayOrder: true
        },
        orderBy: { displayOrder: 'asc' }
      };
    }

    // Get groups with pagination
    const [groups, total] = await Promise.all([
      this.prisma.modifierGroup.findMany({
        where,
        include,
        orderBy: [
          { displayOrder: 'asc' },
          { name: 'asc' }
        ],
        skip,
        take: Number(limit || 20)
      }),
      this.prisma.modifierGroup.count({ where })
    ]);

    return {
      success: true,
      data: groups,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  async getModifierGroupById(id: string) {
    const group = await this.prisma.modifierGroup.findFirst({
      where: {
        id,
        active: true
      },
      include: {
        modifiers: {
          where: { active: true },
          orderBy: { displayOrder: 'asc' }
        },
        _count: {
          select: {
            modifiers: {
              where: { active: true }
            },
            products: true
          }
        }
      }
    });

    if (!group) {
      throw createError('Modifiyer grubu bulunamadı', 404);
    }

    return {
      success: true,
      data: group
    };
  }

  async updateModifierGroup(id: string, data: UpdateModifierGroupInput) {
    // Grup mevcut mu kontrol et
    const existingGroup = await this.prisma.modifierGroup.findFirst({
      where: {
        id,
        active: true
      }
    });

    if (!existingGroup) {
      throw createError('Modifiyer grubu bulunamadı', 404);
    }

    // Business Rule: Modifiyer grup adı benzersiz olmalıdır (kendisi hariç)
    if (data.name) {
      const duplicateName = await this.prisma.modifierGroup.findFirst({
        where: {
          name: data.name,
          active: true,
          NOT: { id }
        }
      });

      if (duplicateName) {
        throw createError('Bu isimde bir modifiyer grubu zaten mevcut', 400);
      }
    }

    const updatedGroup = await this.prisma.modifierGroup.update({
      where: { id },
      data,
      include: {
        _count: {
          select: {
            modifiers: {
              where: { active: true }
            }
          }
        }
      }
    });

    return {
      success: true,
      data: updatedGroup
    };
  }

  async deleteModifierGroup(id: string) {
    // Grup mevcut mu kontrol et
    const existingGroup = await this.prisma.modifierGroup.findFirst({
      where: {
        id,
        active: true
      }
    });

    if (!existingGroup) {
      throw createError('Modifiyer grubu bulunamadı', 404);
    }

    // Business Rule: Modifiyerleri olan grup silinemez
    const modifiers = await this.prisma.modifier.findFirst({
      where: {
        groupId: id,
        active: true
      }
    });

    if (modifiers) {
      throw createError('Modifiyerleri olan grup silinemez', 400);
    }

    // Business Rule: Ürünlerde kullanılan grup silinemez
    const productGroups = await this.prisma.productModifierGroup.findFirst({
      where: {
        modifierGroupId: id
      }
    });

    if (productGroups) {
      throw createError('Ürünlerde kullanılan modifiyer grubu silinemez', 400);
    }

    // Soft delete (active = false)
    await this.prisma.modifierGroup.update({
      where: { id },
      data: { active: false }
    });

    return {
      success: true,
      data: 'Modifiyer grubu başarıyla silindi'
    };
  }

  // ==================== MODIFIER METHODS ====================

  async createModifier(data: CreateModifierInput) {
    // Business Rule: Grup mevcut olmalıdır
    const group = await this.prisma.modifierGroup.findFirst({
      where: {
        id: data.groupId,
        active: true
      }
    });

    if (!group) {
      throw createError('Belirtilen modifiyer grubu bulunamadı', 404);
    }

    // Business Rule: Aynı grupta modifiyer adı benzersiz olmalıdır
    const existingModifier = await this.prisma.modifier.findFirst({
      where: {
        groupId: data.groupId,
        name: data.name,
        active: true
      }
    });

    if (existingModifier) {
      throw createError('Bu grupta aynı isimde bir modifiyer zaten mevcut', 400);
    }

    const modifier = await this.prisma.modifier.create({
      data,
      include: {
        group: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    return {
      success: true,
      data: modifier
    };
  }

  async getModifiers(query: GetModifiersQuery) {
    const { page, limit, groupId, search, active } = query;
    const skip = (Number(page || 1) - 1) * Number(limit || 20);

    // Build where clause
    const where: any = {};

    if (active !== undefined) {
      where.active = active;
    } else {
      where.active = true; // Default olarak sadece aktif modifiyerler
    }

    if (groupId) {
      where.groupId = groupId;
    }

    if (search) {
      where.name = { contains: search, mode: 'insensitive' };
    }

    // Get modifiers with pagination
    const [modifiers, total] = await Promise.all([
      this.prisma.modifier.findMany({
        where,
        include: {
          group: {
            select: {
              id: true,
              name: true
            }
          }
        },
        orderBy: [
          { displayOrder: 'asc' },
          { name: 'asc' }
        ],
        skip,
        take: Number(limit || 20)
      }),
      this.prisma.modifier.count({ where })
    ]);

    return {
      success: true,
      data: modifiers,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  async getModifierById(id: string) {
    const modifier = await this.prisma.modifier.findFirst({
      where: {
        id,
        active: true
      },
      include: {
        group: {
          select: {
            id: true,
            name: true,
            minSelection: true,
            maxSelection: true,
            required: true
          }
        }
      }
    });

    if (!modifier) {
      throw createError('Modifiyer bulunamadı', 404);
    }

    return {
      success: true,
      data: modifier
    };
  }

  async updateModifier(id: string, data: UpdateModifierInput) {
    // Modifiyer mevcut mu kontrol et
    const existingModifier = await this.prisma.modifier.findFirst({
      where: {
        id,
        active: true
      }
    });

    if (!existingModifier) {
      throw createError('Modifiyer bulunamadı', 404);
    }

    // Business Rule: Grup mevcut olmalıdır (eğer değiştiriliyorsa)
    if (data.groupId) {
      const group = await this.prisma.modifierGroup.findFirst({
        where: {
          id: data.groupId,
          active: true
        }
      });

      if (!group) {
        throw createError('Belirtilen modifiyer grubu bulunamadı', 404);
      }
    }

    // Business Rule: Aynı grupta modifiyer adı benzersiz olmalıdır (kendisi hariç)
    if (data.name || data.groupId) {
      const checkGroupId = data.groupId || existingModifier.groupId;
      const checkName = data.name || existingModifier.name;

      const duplicateName = await this.prisma.modifier.findFirst({
        where: {
          groupId: checkGroupId,
          name: checkName,
          active: true,
          NOT: { id }
        }
      });

      if (duplicateName) {
        throw createError('Bu grupta aynı isimde bir modifiyer zaten mevcut', 400);
      }
    }

    const updatedModifier = await this.prisma.modifier.update({
      where: { id },
      data,
      include: {
        group: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    return {
      success: true,
      data: updatedModifier
    };
  }

  async deleteModifier(id: string) {
    // Modifiyer mevcut mu kontrol et
    const existingModifier = await this.prisma.modifier.findFirst({
      where: {
        id,
        active: true
      }
    });

    if (!existingModifier) {
      throw createError('Modifiyer bulunamadı', 404);
    }

    // Business Rule: Aktif siparişlerde kullanılan modifiyer silinemez
    const activeOrderItems = await this.prisma.orderItemModifier.findFirst({
      where: {
        modifierId: id,
        orderItem: {
          order: {
            status: {
              in: ['PENDING', 'PREPARING', 'READY']
            }
          }
        }
      }
    });

    if (activeOrderItems) {
      throw createError('Aktif siparişlerde kullanılan modifiyer silinemez', 400);
    }

    // Soft delete (active = false)
    await this.prisma.modifier.update({
      where: { id },
      data: { active: false }
    });

    return {
      success: true,
      data: 'Modifiyer başarıyla silindi'
    };
  }
}
