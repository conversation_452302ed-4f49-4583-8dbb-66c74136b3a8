/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./src/renderer/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    screens: {
      'sm': '640px',
      'md': '768px',
      'lg': '1024px',
      'xl': '1280px',
      '2xl': '1536px',
      'kiosk': '1920px',   // 👈 Electron kiosk breakpoint
      // Electron specific breakpoints
      'electron-sm': '800px',
      'electron-md': '1024px',
      'electron-lg': '1280px',
      'electron-xl': '1440px',
    },
    extend: {
      colors: {
        // 🎨 ATROPOS POS GLOBAL COLOR PALETTE
        primary: {
          DEFAULT: '#3b82f6',
          50: '#eff6ff',
          100: '#dbeafe',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          foreground: '#ffffff',
        },
        secondary: {
          DEFAULT: '#f1f5f9',
          foreground: '#0f172a',
        },
        destructive: {
          DEFAULT: '#ef4444',
          foreground: '#ffffff',
        },

        // 🎯 SYSTEM COLORS - Tüm sistemde bu renkleri kullan
        background: '#ffffff',
        foreground: '#0f172a',

        // 📦 CARD COLORS
        card: {
          DEFAULT: '#ffffff',
          foreground: '#0f172a',
          border: '#e2e8f0',
        },

        // 🔘 INPUT COLORS
        input: {
          DEFAULT: '#ffffff',
          border: '#d1d5db',
          'border-focus': '#3b82f6',
          placeholder: '#9ca3af',
          text: '#111827',
        },

        // 🎨 GRAY SCALE - Consistent grays
        gray: {
          50: '#f9fafb',
          100: '#f3f4f6',
          200: '#e5e7eb',
          300: '#d1d5db',
          400: '#9ca3af',
          500: '#6b7280',
          600: '#4b5563',
          700: '#374151',
          800: '#1f2937',
          900: '#111827',
        },

        // 🟢 SUCCESS COLORS
        success: {
          50: '#f0fdf4',
          100: '#dcfce7',
          500: '#22c55e',
          600: '#16a34a',
          700: '#15803d',
        },

        // 🔴 ERROR COLORS
        error: {
          50: '#fef2f2',
          100: '#fee2e2',
          500: '#ef4444',
          600: '#dc2626',
          700: '#b91c1c',
        },

        // 🟡 WARNING COLORS
        warning: {
          50: '#fffbeb',
          100: '#fef3c7',
          500: '#f59e0b',
          600: '#d97706',
          700: '#b45309',
        },

        // 🎯 KIOSK GLOBAL COLOR SYSTEM - One-shot renk sistemi
        kiosk: {
          bg: '#f9fafb',        // page background (gray-50)
          card: '#ffffff',      // card / modal background
          input: '#f3f4f6',     // input background (gray-100)
          border: '#e5e7eb',    // border color (gray-200)
          text: '#111827',      // primary text (gray-900)
          muted: '#6b7280',     // secondary text (gray-500)
          primary: '#3b82f6',   // accent blue
          error: '#ef4444',     // error red
          success: '#22c55e',   // success green
          warning: '#f59e0b',   // warning orange
        },

        // Legacy support
        muted: {
          DEFAULT: '#f1f5f9',
          foreground: '#64748b',
        },
        accent: {
          DEFAULT: '#f1f5f9',
          foreground: '#0f172a',
        },
        border: '#e2e8f0',
        ring: '#3b82f6',
      },
      keyframes: {
        slideIn: {
          from: { transform: 'translateX(calc(100% + 1rem))' },
          to: { transform: 'translateX(0)' },
        },
        hide: {
          from: { opacity: '1' },
          to: { opacity: '0' },
        },
        swipeOut: {
          from: { transform: 'translateX(var(--radix-toast-swipe-end-x))' },
          to: { transform: 'translateX(calc(100% + 1rem))' },
        },
      },
      animation: {
        slideIn: 'slideIn 150ms cubic-bezier(0.16, 1, 0.3, 1)',
        hide: 'hide 100ms ease-in',
        swipeOut: 'swipeOut 100ms ease-out',
      },
    },
  },
  plugins: [],
}
