import React from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useAuthStore } from './store/authStore';
import { PinLogin } from './pages/PinLogin';
import { Dashboard } from './pages/Dashboard';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

function App() {
  const { isAuthenticated } = useAuthStore();

  return (
    <QueryClientProvider client={queryClient}>
      <div className="kiosk">
        {isAuthenticated ? (
          <Dashboard />
        ) : (
          <PinLogin />
        )}
      </div>
    </QueryClientProvider>
  );
}

export default App
