import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import * as Dialog from '@radix-ui/react-dialog';
import * as Select from '@radix-ui/react-select';
import * as Toast from '@radix-ui/react-toast';
import { 
  X, 
  Save, 
  Loader2, 
  ChevronDown, 
  Check,
  Package,
  AlertCircle
} from 'lucide-react';
import { Button } from '../common/Button';
import { LoadingSpinner } from '../common/LoadingSpinner';
import { useProductStore } from '../../store/productStore';
import { apiService } from '../../services/api.service';
import { 
  productFormSchema, 
  ProductFormData,
  convertFormDataToCreateProduct
} from '../../lib/validations/product.validation';
import { ProductUnit } from '../../types/product.types';
import { cn } from '../../lib/utils';

interface ProductFormProps {
  open: boolean;
  onClose: () => void;
}

const PRODUCT_UNITS = [
  { value: ProductUnit.PIECE, label: 'Adet' },
  { value: ProductUnit.KG, label: 'Kilogram' },
  { value: ProductUnit.GRAM, label: 'Gram' },
  { value: ProductUnit.LITER, label: 'Litre' },
  { value: ProductUnit.ML, label: 'Mililitre' },
  { value: ProductUnit.PORTION, label: 'Porsiyon' }
];

export const ProductForm: React.FC<ProductFormProps> = ({ open, onClose }) => {
  const queryClient = useQueryClient();
  const {
    selectedProduct,
    productFormMode,
    addProduct,
    updateProduct,
    setLoading,
    setError
  } = useProductStore();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setValue,
    watch,
    control
  } = useForm<ProductFormData>({
    resolver: zodResolver(productFormSchema),
    defaultValues: {
      name: '',
      description: '',
      basePrice: '',
      categoryId: '',
      taxId: '',
      code: '',
      barcode: '',
      image: '',
      unit: ProductUnit.PIECE,
      trackStock: false,
      criticalStock: '',
      available: true,
      sellable: true,
      preparationTime: '',
      displayOrder: ''
    }
  });

  // Load categories for select
  const { data: categoriesData } = useQuery({
    queryKey: ['categories', { limit: 100 }],
    queryFn: () => apiService.getCategories({ limit: 100 }),
    staleTime: 10 * 60 * 1000 // 10 minutes
  });

  // Load taxes for select (assuming we have a tax endpoint)
  const { data: taxesData } = useQuery({
    queryKey: ['taxes'],
    queryFn: () => apiService.getTaxes?.() || Promise.resolve({ success: true, data: [] }),
    staleTime: 30 * 60 * 1000 // 30 minutes
  });

  // Create mutation
  const createMutation = useMutation({
    mutationFn: (data) => {
      console.log('🔥 ÜRÜN EKLEME DEBUG:');
      console.log('📦 apiService:', apiService);
      console.log('📦 Data gönderiliyor:', data);
      console.log('🔑 Auth token:', localStorage.getItem('auth_token'));
      console.log('👤 User data:', JSON.parse(localStorage.getItem('auth-storage') || '{}'));

      if (!apiService) {
        console.error('❌ apiService undefined!');
        throw new Error('apiService undefined');
      }

      if (!apiService.createProduct) {
        console.error('❌ apiService.createProduct undefined!');
        throw new Error('apiService.createProduct undefined');
      }

      return apiService.createProduct(data);
    },
    onSuccess: (response) => {
      console.log('✅ Ürün ekleme başarılı:', response);
      if (response.success) {
        addProduct(response.data);
        queryClient.invalidateQueries({ queryKey: ['products'] });
        onClose();
        reset();
        // Show success toast
      }
    },
    onError: (error) => {
      console.error('❌ Ürün ekleme hatası:', error);
      console.error('❌ Error stack:', error.stack);
      setError({
        message: error instanceof Error ? error.message : 'Ürün oluşturulurken hata oluştu',
        type: 'create'
      });
    }
  });

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => 
      apiService.updateProduct(id, data),
    onSuccess: (response) => {
      if (response.success) {
        updateProduct(response.data);
        queryClient.invalidateQueries({ queryKey: ['products'] });
        onClose();
        reset();
        // Show success toast
      }
    },
    onError: (error) => {
      setError({
        message: error instanceof Error ? error.message : 'Ürün güncellenirken hata oluştu',
        type: 'update'
      });
    }
  });

  // Reset form when modal opens/closes or selected product changes
  React.useEffect(() => {
    if (open && selectedProduct && productFormMode === 'edit') {
      reset({
        name: selectedProduct.name,
        description: selectedProduct.description || '',
        basePrice: selectedProduct.basePrice.toString(),
        categoryId: selectedProduct.categoryId,
        taxId: selectedProduct.taxId,
        code: selectedProduct.code,
        barcode: selectedProduct.barcode || '',
        image: selectedProduct.image || '',
        unit: selectedProduct.unit,
        trackStock: selectedProduct.trackStock,
        criticalStock: selectedProduct.criticalStock?.toString() || '',
        available: selectedProduct.available,
        sellable: selectedProduct.sellable,
        preparationTime: selectedProduct.preparationTime?.toString() || '',
        displayOrder: selectedProduct.displayOrder.toString()
      });
    } else if (open && productFormMode === 'create') {
      reset({
        name: '',
        description: '',
        basePrice: '',
        categoryId: '',
        taxId: '',
        code: '',
        barcode: '',
        image: '',
        unit: ProductUnit.PIECE,
        trackStock: false,
        criticalStock: '',
        available: true,
        sellable: true,
        preparationTime: '',
        displayOrder: ''
      });
    }
  }, [open, selectedProduct, productFormMode, reset]);

  const onSubmit = (data: ProductFormData) => {
    console.log('🚀 ÜRÜN FORM SUBMIT DEBUG:');
    console.log('📝 Form data:', data);

    const productData = convertFormDataToCreateProduct(data);
    console.log('🔄 Converted data:', productData);
    console.log('🎯 Form mode:', productFormMode);

    if (productFormMode === 'create') {
      console.log('➕ CREATE modunda, mutation çağrılıyor...');
      createMutation.mutate(productData);
    } else if (selectedProduct) {
      console.log('✏️ UPDATE modunda, mutation çağrılıyor...');
      updateMutation.mutate({
        id: selectedProduct.id,
        data: productData
      });
    }
  };

  const isLoading = createMutation.isPending || updateMutation.isPending;

  return (
    <Dialog.Root open={open} onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 z-50" />
        <Dialog.Content
          className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-kiosk-card rounded-lg shadow-xl z-50 w-full max-w-2xl max-h-[90vh] overflow-y-auto"
          aria-describedby="product-form-desc"
        >
          <div className="p-6">
            <span id="product-form-desc" className="sr-only">Ürün ekleme ve düzenleme formu</span>
            <div className="sticky top-0 bg-kiosk-card z-10 flex items-center justify-between mb-6 pb-4 border-b border-kiosk-border">
              <div>
                <Dialog.Title className="text-lg font-semibold text-kiosk-text">
                  {productFormMode === 'create' ? 'Yeni Ürün Ekle' : 'Ürün Düzenle'}
                </Dialog.Title>
                <Dialog.Description className="text-sm text-kiosk-muted mt-1">
                  {productFormMode === 'create'
                    ? 'Yeni bir ürün oluşturun ve satışa sunun.'
                    : 'Ürün bilgilerini güncelleyin.'
                  }
                </Dialog.Description>
              </div>
              <Dialog.Close asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <X className="w-4 h-4" />
                </Button>
              </Dialog.Close>
            </div>

            <form onSubmit={handleSubmit(onSubmit, (errors) => {
              console.log('❌ FORM VALIDATION HATALARI:', errors);
              console.log('❌ Form geçersiz, submit edilmiyor!');
            })} className="space-y-6">
              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium text-kiosk-text">Temel Bilgiler</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-kiosk-text mb-1">
                      Ürün Adı *
                    </label>
                    <input
                      {...register('name')}
                      type="text"
                      className={cn(
                        "w-full px-3 py-2 border rounded-md text-sm bg-kiosk-input",
                        "focus:outline-none focus:ring-2 focus:ring-kiosk-primary focus:border-kiosk-primary",
                        errors.name ? "border-kiosk-error" : "border-kiosk-border"
                      )}
                      placeholder="Ürün adını giriniz"
                    />
                    {errors.name && (
                      <p className="mt-1 text-xs text-red-600">{errors.name.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-kiosk-text mb-1">
                      Ürün Kodu *
                    </label>
                    <input
                      {...register('code')}
                      type="text"
                      className={cn(
                        "w-full px-3 py-2 border rounded-md text-sm font-mono bg-kiosk-input",
                        "focus:outline-none focus:ring-2 focus:ring-kiosk-primary focus:border-kiosk-primary",
                        errors.code ? "border-kiosk-error" : "border-kiosk-border"
                      )}
                      placeholder="PRD001"
                    />
                    {errors.code && (
                      <p className="mt-1 text-xs text-red-600">{errors.code.message}</p>
                    )}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-kiosk-text mb-1">
                    Açıklama
                  </label>
                  <textarea
                    {...register('description')}
                    rows={3}
                    className={cn(
                      "w-full px-3 py-2 border rounded-md text-sm resize-none bg-kiosk-input",
                      "focus:outline-none focus:ring-2 focus:ring-kiosk-primary focus:border-transparent",
                      errors.description ? "border-kiosk-error" : "border-kiosk-border"
                    )}
                    placeholder="Ürün açıklaması (isteğe bağlı)"
                  />
                  {errors.description && (
                    <p className="mt-1 text-xs text-red-600">{errors.description.message}</p>
                  )}
                </div>
              </div>

              {/* Pricing & Category */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium text-kiosk-text">Fiyat ve Kategori</h3>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-kiosk-text mb-1">
                      Fiyat *
                    </label>
                    <input
                      {...register('basePrice')}
                      type="number"
                      step="0.01"
                      min="0"
                      className={cn(
                        "w-full px-3 py-2 border rounded-md text-sm bg-kiosk-input",
                        "focus:outline-none focus:ring-2 focus:ring-kiosk-primary focus:border-transparent",
                        errors.basePrice ? "border-kiosk-error" : "border-kiosk-border"
                      )}
                      placeholder="0.00"
                    />
                    {errors.basePrice && (
                      <p className="mt-1 text-xs text-red-600">{errors.basePrice.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-kiosk-text mb-1">
                      Birim *
                    </label>
                    <Select.Root
                      value={watch('unit')}
                      onValueChange={(value) => setValue('unit', value as ProductUnit)}
                    >
                      <Select.Trigger className={cn(
                        "w-full px-3 py-2 border rounded-md text-sm bg-kiosk-input",
                        "focus:outline-none focus:ring-2 focus:ring-kiosk-primary focus:border-transparent",
                        "flex items-center justify-between",
                        errors.unit ? "border-kiosk-error" : "border-kiosk-border"
                      )}>
                        <Select.Value placeholder="Birim seçiniz" />
                        <Select.Icon>
                          <ChevronDown className="w-4 h-4" />
                        </Select.Icon>
                      </Select.Trigger>
                      <Select.Portal>
                        <Select.Content className="bg-kiosk-card border border-kiosk-border rounded-md shadow-lg z-50">
                          <Select.Viewport className="p-1">
                            {PRODUCT_UNITS.map((unit) => (
                              <Select.Item
                                key={unit.value}
                                value={unit.value}
                                className="px-3 py-2 text-sm cursor-pointer hover:bg-kiosk-input rounded flex items-center justify-between"
                              >
                                <Select.ItemText>{unit.label}</Select.ItemText>
                                <Select.ItemIndicator>
                                  <Check className="w-4 h-4" />
                                </Select.ItemIndicator>
                              </Select.Item>
                            ))}
                          </Select.Viewport>
                        </Select.Content>
                      </Select.Portal>
                    </Select.Root>
                    {errors.unit && (
                      <p className="mt-1 text-xs text-red-600">{errors.unit.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-kiosk-text mb-1">
                      Kategori *
                    </label>
                    <Select.Root
                      value={watch('categoryId')}
                      onValueChange={(value) => setValue('categoryId', value)}
                    >
                      <Select.Trigger className={cn(
                        "w-full px-3 py-2 border rounded-md text-sm bg-kiosk-input",
                        "focus:outline-none focus:ring-2 focus:ring-kiosk-primary focus:border-transparent",
                        "flex items-center justify-between",
                        errors.categoryId ? "border-kiosk-error" : "border-kiosk-border"
                      )}>
                        <Select.Value placeholder="Kategori seçiniz" />
                        <Select.Icon>
                          <ChevronDown className="w-4 h-4" />
                        </Select.Icon>
                      </Select.Trigger>
                      <Select.Portal>
                        <Select.Content className="bg-kiosk-card border border-kiosk-border rounded-md shadow-lg z-50 max-h-60 overflow-y-auto">
                          <Select.Viewport className="p-1">
                            {categoriesData?.data?.map((category) => (
                              <Select.Item
                                key={category.id}
                                value={category.id}
                                className="px-3 py-2 text-sm cursor-pointer hover:bg-kiosk-input rounded flex items-center justify-between"
                              >
                                <div className="flex items-center gap-2">
                                  <div 
                                    className="w-3 h-3 rounded-full"
                                    style={{ backgroundColor: category.color || '#6B7280' }}
                                  />
                                  <Select.ItemText>{category.name}</Select.ItemText>
                                </div>
                                <Select.ItemIndicator>
                                  <Check className="w-4 h-4" />
                                </Select.ItemIndicator>
                              </Select.Item>
                            ))}
                          </Select.Viewport>
                        </Select.Content>
                      </Select.Portal>
                    </Select.Root>
                    {errors.categoryId && (
                      <p className="mt-1 text-xs text-red-600">{errors.categoryId.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-kiosk-text mb-1">
                      Vergi Oranı *
                    </label>
                    <Select.Root
                      value={watch('taxId')}
                      onValueChange={(value) => setValue('taxId', value)}
                    >
                      <Select.Trigger className={cn(
                        "w-full px-3 py-2 border rounded-md text-sm bg-kiosk-input",
                        "focus:outline-none focus:ring-2 focus:ring-kiosk-primary focus:border-transparent",
                        "flex items-center justify-between",
                        errors.taxId ? "border-kiosk-error" : "border-kiosk-border"
                      )}>
                        <Select.Value placeholder="Vergi oranı seçiniz" />
                        <Select.Icon>
                          <ChevronDown className="w-4 h-4" />
                        </Select.Icon>
                      </Select.Trigger>
                      <Select.Portal>
                        <Select.Content className="bg-kiosk-card border border-kiosk-border rounded-md shadow-lg z-50 max-h-60 overflow-y-auto">
                          <Select.Viewport className="p-1">
                            {taxesData?.data?.map((tax) => (
                              <Select.Item
                                key={tax.id}
                                value={tax.id}
                                className="px-3 py-2 text-sm cursor-pointer hover:bg-kiosk-input rounded flex items-center justify-between"
                              >
                                <Select.ItemText>{tax.name} (%{tax.rate})</Select.ItemText>
                                <Select.ItemIndicator>
                                  <Check className="w-4 h-4" />
                                </Select.ItemIndicator>
                              </Select.Item>
                            ))}
                          </Select.Viewport>
                        </Select.Content>
                      </Select.Portal>
                    </Select.Root>
                    {errors.taxId && (
                      <p className="mt-1 text-xs text-red-600">{errors.taxId.message}</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Additional Fields */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium text-kiosk-text">Ek Bilgiler</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-kiosk-text mb-1">
                      Barkod
                    </label>
                    <input
                      {...register('barcode')}
                      type="text"
                      className={cn(
                        "w-full px-3 py-2 border rounded-md text-sm font-mono bg-kiosk-input",
                        "focus:outline-none focus:ring-2 focus:ring-kiosk-primary focus:border-transparent",
                        errors.barcode ? "border-kiosk-error" : "border-kiosk-border"
                      )}
                      placeholder="1234567890123"
                    />
                    {errors.barcode && (
                      <p className="mt-1 text-xs text-red-600">{errors.barcode.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-kiosk-text mb-1">
                      Hazırlık Süresi (dk)
                    </label>
                    <input
                      {...register('preparationTime')}
                      type="number"
                      min="0"
                      max="1440"
                      className={cn(
                        "w-full px-3 py-2 border rounded-md text-sm bg-kiosk-input",
                        "focus:outline-none focus:ring-2 focus:ring-kiosk-primary focus:border-transparent",
                        errors.preparationTime ? "border-kiosk-error" : "border-kiosk-border"
                      )}
                      placeholder="15"
                    />
                    {errors.preparationTime && (
                      <p className="mt-1 text-xs text-red-600">{errors.preparationTime.message}</p>
                    )}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-kiosk-text mb-1">
                    Ürün Görseli (URL)
                  </label>
                  <input
                    {...register('image')}
                    type="url"
                    className={cn(
                      "w-full px-3 py-2 border rounded-md text-sm bg-kiosk-input",
                      "focus:outline-none focus:ring-2 focus:ring-kiosk-primary focus:border-transparent",
                      errors.image ? "border-kiosk-error" : "border-kiosk-border"
                    )}
                    placeholder="https://example.com/image.jpg"
                  />
                  {errors.image && (
                    <p className="mt-1 text-xs text-red-600">{errors.image.message}</p>
                  )}
                </div>
              </div>

              {/* Checkboxes */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium text-kiosk-text">Durum</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <label className="flex items-center gap-2">
                    <input
                      {...register('available')}
                      type="checkbox"
                      className="rounded border-kiosk-border text-kiosk-primary focus:ring-kiosk-primary"
                    />
                    <span className="text-sm text-kiosk-text">Mevcut</span>
                  </label>

                  <label className="flex items-center gap-2">
                    <input
                      {...register('sellable')}
                      type="checkbox"
                      className="rounded border-kiosk-border text-kiosk-primary focus:ring-kiosk-primary"
                    />
                    <span className="text-sm text-kiosk-text">Satışta</span>
                  </label>

                  <label className="flex items-center gap-2">
                    <input
                      {...register('trackStock')}
                      type="checkbox"
                      className="rounded border-kiosk-border text-kiosk-primary focus:ring-kiosk-primary"
                    />
                    <span className="text-sm text-kiosk-text">Stok Takipli</span>
                  </label>
                </div>

                {watch('trackStock') && (
                  <div>
                    <label className="block text-sm font-medium text-kiosk-text mb-1">
                      Kritik Stok Seviyesi
                    </label>
                    <input
                      {...register('criticalStock')}
                      type="number"
                      min="0"
                      step="0.001"
                      className={cn(
                        "w-full px-3 py-2 border rounded-md text-sm bg-kiosk-input",
                        "focus:outline-none focus:ring-2 focus:ring-kiosk-primary focus:border-transparent",
                        errors.criticalStock ? "border-kiosk-error" : "border-kiosk-border"
                      )}
                      placeholder="10"
                    />
                    {errors.criticalStock && (
                      <p className="mt-1 text-xs text-red-600">{errors.criticalStock.message}</p>
                    )}
                  </div>
                )}
              </div>

              {/* Form Actions */}
              <div className="flex items-center justify-end gap-3 pt-6 border-t border-kiosk-border">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                  disabled={isLoading}
                >
                  İptal
                </Button>
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="min-w-[100px]"
                  onClick={() => {
                    console.log('🖱️ OLUŞTUR BUTONUNA TIKLANDI!');
                    console.log('🔍 Form errors:', errors);
                    console.log('🔍 isSubmitting:', isSubmitting);
                    console.log('🔍 isLoading:', isLoading);
                  }}
                >
                  {isLoading ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      {productFormMode === 'create' ? 'Oluştur' : 'Güncelle'}
                    </>
                  )}
                </Button>
              </div>
            </form>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};
